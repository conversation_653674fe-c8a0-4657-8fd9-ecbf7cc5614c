# 💳 Credit Card Management System

This project was developed during summer internship at **ATB** (Arab Tunisian Bank), aiming to modernize and optimize the internal process of **credit card storage, tracking, delivery, and customer retrieval prediction**.

## 🎯 Objective

To replace manual tracking with a smart, full-stack solution that leverages OCR, predictive analytics, and modern web/mobile development practices.

---

## 🚀 Key Features

### 📷 OCR-Based Card Scanning
- Mobile and web interface allow agents to **scan physical cards** using the device camera.
- Uses **Optical Character Recognition (OCR)** to **extract cardholder data automatically**.

### 📦 Smart Storage Management
- Each card is linked to a **physical storage location** (drawer, slot) in the system.

### 🔍 Intelligent Search & NLP Matching
- Quickly search for cards using **partial or fuzzy data** (name misspellings or incomplete info).
- Leverages **Natural Language Processing** for enhanced matching.

### 📊 Real-Time Admin Dashboard
- Visualizes:
  - Number of pending, available, and delivered cards
  - Card retrieval times
  - Agent performance

### ⏳ Predictive Arrival Modeling
- Machine Learning module predicts **when a customer is likely to come** retrieve their card.
- Helps agents prepare and reduce wait time.

### 🔔 Notifications & Alerts 
- Alert agents of spikes in arrival volume.

---

## 🧠 Technologies Used

| Layer        | Tech Stack                     |
|--------------|---------------------------------|
| Frontend     | Flutter (Web + Mobile)          |
| Backend      | Node.js + Express.js            |
| Database     | MongoDB + Mongoose              |
| OCR Engine   | Tesseract.js / Google Vision API |
| ML Module    | Python (Scikit-learn or TensorFlow) |
| Dev Tools    | Postman, Git, VS Code, Nodemon  |


## 🧑‍💻 Authors

- 👨‍💻 **[Wardi Aziz](https://www.linkedin.com/in/aziz-wardi-531759104/)**
- 👩‍💻 **[Abdellaoui Malek](https://www.linkedin.com/in/malek-abdellaoui)**

---

## 📄 License

This project is for academic and internship purposes only and is not intended for commercial use.
