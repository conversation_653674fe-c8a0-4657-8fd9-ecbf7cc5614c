const express = require('express');
const router = express.Router();
const agentController = require('../controllers/agentController');

router.get('/getAllClients', agentController.getAllClients);
router.get('/fullname/:fullName', agentController.getClientByFullName);
router.put('/updateClientAccount/:id', agentController.updateClient);
router.put('/deactivateClient/:id', agentController.deactivateClient);

module.exports = router;
