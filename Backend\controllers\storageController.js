const Storage = require('../models/storage');
const Card = require('../models/card');
const { validationResult, body } = require('express-validator');


// Get all storage slots
exports.getAllStorageSlots = async (req, res) => {
    try {
        const { page = 1, limit = 50, occupied } = req.query;
        const query = {};
        
        if (occupied !== undefined) {
            query.occupied = occupied === 'true';
        }

        const storageSlots = await Storage.find(query)
            .limit(limit * 1)
            .skip((page - 1) * limit)
            .sort({ location: 1 });

        const total = await Storage.countDocuments(query);

        res.json({
            storageSlots,
            totalPages: Math.ceil(total / limit),
            currentPage: page,
            total
        });
    } catch (error) {
        console.error('Error fetching storage slots:', error);
        res.status(500).json({ message: 'Server error' });
    }
};

// Get available storage slots
exports.getAvailableSlots = async (req, res) => {
    try {
        const availableSlots = await Storage.find({ occupied: false })
            .sort({ location: 1 });

        res.json({
            availableSlots,
            count: availableSlots.length
        });
    } catch (error) {
        console.error('Error fetching available slots:', error);
        res.status(500).json({ message: 'Server error' });
    }
};

// Get occupied storage slots
exports.getOccupiedSlots = async (req, res) => {
    try {
        const occupiedSlots = await Storage.find({ occupied: true })
            .sort({ location: 1 });

        // Get card details for occupied slots
        const slotsWithCards = await Promise.all(
            occupiedSlots.map(async (slot) => {
                const card = await Card.findOne({ storageSlot: slot.location });
                return {
                    ...slot.toObject(),
                    card: card || null
                };
            })
        );

        res.json({
            occupiedSlots: slotsWithCards,
            count: occupiedSlots.length
        });
    } catch (error) {
        console.error('Error fetching occupied slots:', error);
        res.status(500).json({ message: 'Server error' });
    }
};

// Get storage capacity report
exports.getStorageCapacityReport = async (req, res) => {
    try {
        const totalSlots = await Storage.countDocuments();
        const occupiedSlots = await Storage.countDocuments({ occupied: true });
        const availableSlots = totalSlots - occupiedSlots;
        const utilizationRate = totalSlots > 0 ? (occupiedSlots / totalSlots * 100).toFixed(2) : 0;

        const capacityByLocation = await Storage.aggregate([
            {
                $group: {
                    _id: { $substr: ['$location', 0, 3] }, // Group by prefix
                    total: { $sum: 1 },
                    occupied: { $sum: { $cond: ['$occupied', 1, 0] } },
                    available: { $sum: { $cond: ['$occupied', 0, 1] } }
                }
            },
            {
                $project: {
                    prefix: '$_id',
                    total: 1,
                    occupied: 1,
                    available: 1,
                    utilizationRate: {
                        $round: [{ $multiply: [{ $divide: ['$occupied', '$total'] }, 100] }, 2]
                    }
                }
            }
        ]);

        res.json({
            summary: {
                totalSlots,
                occupiedSlots,
                availableSlots,
                utilizationRate: parseFloat(utilizationRate)
            },
            byLocation: capacityByLocation
        });
    } catch (error) {
        console.error('Error generating capacity report:', error);
        res.status(500).json({ message: 'Server error' });
    }
};


// Release storage slot
exports.releaseSlot = async (req, res) => {
    try {
        const { storageLocation } = req.params;

        const storage = await Storage.findOne({ location: storageLocation });
        if (!storage) {
            return res.status(404).json({ message: 'Storage location not found' });
        }

        // Update storage
        storage.occupied = false;
        await storage.save();

        // Update card if exists
        await Card.updateOne(
            { storageSlot: storageLocation },
            { $unset: { storageSlot: 1 } }
        );

        res.json({
            message: 'Storage slot released successfully',
            storage
        });
    } catch (error) {
        console.error('Error releasing storage slot:', error);
        res.status(500).json({ message: 'Server error' });
    }
};


// Get storage utilization
exports.getStorageUtilization = async (req, res) => {
    try {
        const totalSlots = await Storage.countDocuments();
        const occupiedSlots = await Storage.countDocuments({ occupied: true });
        const utilizationPercentage = totalSlots > 0 ? (occupiedSlots / totalSlots * 100).toFixed(2) : 0;

        // Get utilization by hour (for cards stored today)
        const today = new Date();
        today.setHours(0, 0, 0, 0);

        const hourlyUtilization = await Card.aggregate([
            {
                $match: {
                    receivedAt: { $gte: today },
                    storageSlot: { $exists: true }
                }
            },
            {
                $group: {
                    _id: { $hour: '$receivedAt' },
                    count: { $sum: 1 }
                }
            },
            {
                $sort: { '_id': 1 }
            }
        ]);

        res.json({
            totalSlots,
            occupiedSlots,
            availableSlots: totalSlots - occupiedSlots,
            utilizationPercentage: parseFloat(utilizationPercentage),
            hourlyUtilization
        });
    } catch (error) {
        console.error('Error getting storage utilization:', error);
        res.status(500).json({ message: 'Server error' });
    }
};

// Validate slot availability
exports.validateSlotAvailability = async (req, res) => {
    try {
        const { storageLocation } = req.params;

        const storage = await Storage.findOne({ location: storageLocation });
        if (!storage) {
            return res.status(404).json({
                available: false,
                message: 'Storage location not found'
            });
        }

        const isAvailable = !storage.occupied;

        res.json({
            available: isAvailable,
            storage,
            message: isAvailable ? 'Storage slot is available' : 'Storage slot is occupied'
        });
    } catch (error) {
        console.error('Error validating slot availability:', error);
        res.status(500).json({ message: 'Server error' });
    }
};
