const { validationResult, body } = require('express-validator');
const Card = require('../models/card');
const Client = require('../models/client');


//Scan card and retrieve client data for form population
exports.scanCard = [
    // Validation middleware
    body('cardNumber').notEmpty().withMessage('Card number is required'),
    body('clientName').notEmpty().withMessage('Client name is required'),
    body('expirationDate').optional().isISO8601().withMessage('Invalid expiration date format'),

    async (req, res) => {
        try {
            // Check for validation errors
            const errors = validationResult(req);
            if (!errors.isEmpty()) {
                return res.status(400).json({
                    success: false,
                    message: 'Validation failed',
                    errors: errors.array()
                });
            }

            const { cardNumber, clientName, expirationDate } = req.body;

            // Check if card already exists in database
            const existingCard = await Card.findOne({ cardNumber });
            if (existingCard) {
                return res.status(409).json({
                    success: false,
                    message: 'Card already exists in the system',
                    card: existingCard
                });
            }

            // Find client by name (case-insensitive search)
            const client = await Client.findOne({ 
                fullName: new RegExp(`^${clientName.trim()}$`, 'i') 
            });

            if (!client) {
                return res.status(404).json({
                    success: false,
                    message: 'Client not found in database',
                    suggestion: 'Please verify the client name or create a new client record'
                });
            }

            // Prepare card data for form population
            const cardData = {
                cardNumber,
                expirationDate: expirationDate || null,
                status: 'Pending',
                clientId: client._id,
                receivedAt: new Date(),
                agentId: req.user ? req.user.id : null
            };

            // Return combined data for form population
            const formData = {
                success: true,
                message: 'Card scanned successfully, client found',
                cardData: cardData,
                clientData: {
                    _id: client._id,
                    fullName: client.fullName,
                    cin: client.cin,
                    phone: client.phone,
                    dateOfBirth: client.dateOfBirth,
                    nationality: client.nationality,
                    occupation: client.occupation,
                    registeredAt: client.registeredAt
                },
                formReady: true
            };

        

            res.status(200).json(formData);

        } catch (error) {
            console.error('Card scan error:', error);
            
        

            res.status(500).json({
                success: false,
                message: 'Error processing card scan',
                error: error.message
            });
        }
    }
];

//Save card data after form completion
exports.saveCard = [
    // Validation middleware
    body('cardNumber').notEmpty().withMessage('Card number is required'),
    body('clientId').notEmpty().withMessage('Client ID is required'),
    body('expirationDate').optional().isISO8601().withMessage('Invalid expiration date format'),
    body('storageSlot').optional().isString().withMessage('Storage slot must be a string'),

    async (req, res) => {
        try {
            // Check for validation errors
            const errors = validationResult(req);
            if (!errors.isEmpty()) {
                return res.status(400).json({
                    success: false,
                    message: 'Validation failed',
                    errors: errors.array()
                });
            }

            const { 
                cardNumber, 
                clientId, 
                expirationDate, 
                storageSlot,
                status = 'Pending'
            } = req.body;


            // Check if card already exists
            const existingCard = await Card.findOne({ cardNumber });
            if (existingCard) {
                return res.status(409).json({
                    success: false,
                    message: 'Card already exists in the system'
                });
            }

            // Create new card record
            const newCard = new Card({
                cardNumber,
                expirationDate: expirationDate || null,
                status:"Pending",
                storageSlot: storageSlot || null,
                receivedAt: new Date(),
                clientId,
                agentId: req.user ? req.user.id : null
            });

            // Save card to database
            const savedCard = await newCard.save();

           

            res.status(201).json({
                success: true,
                message: 'Card saved successfully',
                card: savedCard,
                client: {
                    _id: client._id,
                    fullName: client.fullName,
                    cin: client.cin,
                    phone: client.phone
                }
            });

        } catch (error) {
            console.error('Card save error:', error);
            
        

            res.status(500).json({
                success: false,
                message: 'Error saving card',
                error: error.message
            });
        }
    }
];

//Get card and client information by card number
exports.getCardInfo = async (req, res) => {
    try {
        const { cardNumber } = req.params;

        // Find card by card number
        const card = await Card.findOne({ cardNumber });
        if (!card) {
            return res.status(404).json({
                success: false,
                message: 'Card not found'
            });
        }

        // Find associated client
        const client = await Client.findById(card.clientId);
        if (!client) {
            return res.status(404).json({
                success: false,
                message: 'Associated client not found'
            });
        }

        res.status(200).json({
            success: true,
            card: card,
            client: client
        });

    } catch (error) {
        console.error('Get card info error:', error);
        res.status(500).json({
            success: false,
            message: 'Error retrieving card information',
            error: error.message
        });
    }
};

//Update card information
exports.updateCard = [
    body('expirationDate').optional().isISO8601().withMessage('Invalid expiration date format'),
    body('status').optional().isIn(['Pending', 'Delivered']).withMessage('Invalid status'),
    body('storageSlot').optional().isString().withMessage('Storage slot must be a string'),

    async (req, res) => {
        try {
            const errors = validationResult(req);
            if (!errors.isEmpty()) {
                return res.status(400).json({
                    success: false,
                    message: 'Validation failed',
                    errors: errors.array()
                });
            }

            const { cardNumber } = req.params;
            const updateData = req.body;

            // Add delivery timestamp if status is being changed to Delivered
            if (updateData.status === 'Delivered') {
                updateData.deliveredAt = new Date();
            }

            const updatedCard = await Card.findOneAndUpdate(
                { cardNumber },
                updateData,
                { new: true }
            );

            if (!updatedCard) {
                return res.status(404).json({
                    success: false,
                    message: 'Card not found'
                });
            }

            
            res.status(200).json({
                success: true,
                message: 'Card updated successfully',
                card: updatedCard
            });

        } catch (error) {
            console.error('Card update error:', error);
            res.status(500).json({
                success: false,
                message: 'Error updating card',
                error: error.message
            });
        }
    }
];
