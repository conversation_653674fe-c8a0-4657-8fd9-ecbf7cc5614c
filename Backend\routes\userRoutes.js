const express = require('express');
const router = express.Router();
const registerController = require('../controllers/registerController');
const loginController = require('../controllers/loginController');
const authorize = require('../middleware/authorize');


router.post('/register', authorize(['admin']), (req, res) => {
  res.json({ message: 'You are authorized as admin' });
});

router.post('/login', loginController.login);
router.post('/logout', loginController.logout);

module.exports = router;
