const mongoose = require('mongoose');

const cardSchema = new mongoose.Schema({
  cardNumber: { type: String, required: true },
  expirationDate: { type: Date },
  status: { type: String, enum: ['Pending', 'Delivered'], default: 'Pending' },
  storageSlot: { type: String },
  receivedAt: { type: Date },
  deliveredAt: { type: Date },
  clientId: { type: String, required: true },
  agentId: { type: String }
});

module.exports = mongoose.model('Card', cardSchema);
