# === General ===
node_modules
dist
dist-ssr
.log
logs
.local
.DS_Store

# === npmyarnpnpm debug logs ===
npm-debug.log
yarn-debug.log
yarn-error.log
pnpm-debug.log
lerna-debug.log

# === Frontend Build ===
frontendnode_modules
frontenddist
frontendbuild

# === Backend Build ===
backendnode_modules
backenddist
backendbuild

# === Environment files ===
.env
.env.local
.env.development.local
.env.test.local
.env.production.local

# === VSCode ===
.vscode
!.vscodeextensions.json

# === IDE files ===
.idea
.suo
.ntvs
.njsproj
.sln
.sw

# === OS Files ===
Thumbs.db
ehthumbs.db
