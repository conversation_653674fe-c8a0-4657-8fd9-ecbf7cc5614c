const bcrypt = require('bcrypt');
const { validationResult, body } = require('express-validator');
const User = require('../models/User');
const sendEmail = require('../utils/sendEmail');

exports.register = [
    body('email').isEmail().normalizeEmail(),
    body('password').isLength({ min: 6 }).trim().escape(),
    body('firstName').isLength({ min: 2 }).trim().escape(),
    body('lastName').isLength({ min: 2 }).trim().escape(),
    body('branch').isLength({ min: 2 }).trim().escape(),
    body('role').optional().isIn(['agent', 'admin', '']).trim().escape(),

    async (req, res) => {
        const errors = validationResult(req);
        if (!errors.isEmpty()) {
            return res.status(400).json({ errors: errors.array() });
        }

        const { email, password, firstName, lastName, branch, role } = req.body;

        try {
            let user = await User.findOne({ email });
            if (user) {
                return res.status(400).json({ message: 'User already exists' });
            }

            const salt = await bcrypt.genSalt(10);
            const hashedPassword = await bcrypt.hash(password, salt);

            user = new User({
                firstName,
                lastName,
                email,
                password: hashedPassword,
                branch,
                role,
            });

            await user.save();
            await sendEmail(user.email, user.firstName, user.email, password);


            res.status(201).json({ message: 'User registered successfully.' });
        } catch (error) {
            console.error('Error during registration:', error);
            res.status(500).json({ message: 'Server error' });
        }
    }
];