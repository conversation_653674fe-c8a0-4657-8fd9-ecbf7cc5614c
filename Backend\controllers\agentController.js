const client = require('../models/client');

// Get clients
exports.getAllClients = async (req, res) => {
  try {
    const clients = await client.find();
    res.json(clients);
  } catch (err) {
    res.status(500).json({ error: err.message });
  }
};

//Get client by name
exports.getClientByFullName = async (req, res) => {
  try {
    const fullName = req.params.fullName.trim();

    const client = await Client.findOne({ fullName: new RegExp(`^${fullName}$`, 'i') });

    if (!client) {
      return res.status(404).json({ message: 'Client not found' });
    }

    res.status(200).json(client);
  } catch (error) {
    res.status(500).json({ error: 'Server error' });
  }
};

//Update client
exports.updateClient = async (req, res) => {
  try {
    const clientId = req.params.id;
    const updateData = req.body;

    const updatedClient = await Client.findByIdAndUpdate(
      clientId,
      updateData,
      { new: true }
    );

    if (!updatedClient) {
      return res.status(404).json({ message: 'Client not found' });
    }

    res.status(200).json(updatedClient);
  } catch (error) {
    console.error('Error updating client:', error);
    res.status(500).json({ message: 'Server error' });
  }
};

// Deactivate client
exports.deactivateClient = async (req, res) => {
    try {
        const { id } = req.params;

        const client = await Client.findById(id);
        if (!client) {
            return res.status(404).json({ message: 'Client not found' });
        }

        client.status = 'inactive';
        await client.save();

        res.json({ 
            message: 'Client deactivated successfully',
            client: client
        });

    } catch (error) {
        console.error('Error deactivating client:', error);
        res.status(500).json({ message: 'Server error' });
    }
};
