const Client = require('../models/client');
const Card = require('../models/card');

// Get clients
exports.getAllClients = async (req, res) => {
  try {
    const clients = await Client.find();
    res.json(clients);
  } catch (err) {
    res.status(500).json({ error: err.message });
  }
};

//Get client by name
exports.getClientByFullName = async (req, res) => {
  try {
    const fullName = req.params.fullName.trim();

    const foundClient = await Client.findOne({ fullName: new RegExp(`^${fullName}$`, 'i') });

    if (!foundClient) {
      return res.status(404).json({ message: 'Client not found' });
    }

    // Log client search
    if (req.user) {
      await LogService.logAgentAction(req.user.id, `Searched for client: ${fullName}`);
    }

    res.status(200).json(foundClient);
  } catch (error) {
    res.status(500).json({ error: 'Server error' });
  }
};

//Get client by card number
exports.getClientByCardNumber = async (req, res) => {
  try {
    const cardNumber = req.params.cardNumber.trim();
    const card = await Card.findOne({ cardNumber });

    if (!card) {
      return res.status(404).json({ message: 'Card not found' });
    }

    const client = await Client.findById(card.clientId);

    if (!client) {
      return res.status(404).json({ message: 'Client not found for this card' });
    }

    // Log card search
    if (req.user) {
      await LogService.logAgentAction(req.user.id, `Searched for client by card number: ${cardNumber}`);
    }

    res.status(200).json(client);

  } catch (error) {
    console.error('Error getting client by card number:', error);
    res.status(500).json({ message: 'Server error' });
  }
};