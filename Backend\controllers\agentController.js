const Client = require('../models/client');

// Get clients
exports.getAllClients = async (req, res) => {
  try {
    const clients = await Client.find();
    res.json(clients);
  } catch (err) {
    res.status(500).json({ error: err.message });
  }
};

//Get client by name
exports.getClientByFullName = async (req, res) => {
  try {
    const fullName = req.params.fullName.trim();

    const foundClient = await Client.findOne({ fullName: new RegExp(`^${fullName}$`, 'i') });

    if (!foundClient) {
      return res.status(404).json({ message: 'Client not found' });
    }

    // Log client search
    if (req.user) {
      await LogService.logAgentAction(req.user.id, `Searched for client: ${fullName}`);
    }

    res.status(200).json(foundClient);
  } catch (error) {
    res.status(500).json({ error: 'Server error' });
  }
};

//Update client
exports.updateClient = async (req, res) => {
  try {
    const clientId = req.params.id;
    const updateData = req.body;

    const updatedClient = await Client.findByIdAndUpdate(
      clientId,
      updateData,
      { new: true }
    );

    if (!updatedClient) {
      return res.status(404).json({ message: 'Client not found' });
    }

    // Log client update
    if (req.user) {
      await LogService.logAgentAction(req.user.id, `Updated client: ${updatedClient.fullName}`);
    }

    res.status(200).json(updatedClient);
  } catch (error) {
    console.error('Error updating client:', error);
    res.status(500).json({ message: 'Server error' });
  }
};

// Deactivate client
exports.deactivateClient = async (req, res) => {
    try {
        const { id } = req.params;

        const foundClient = await Client.findById(id);
        if (!foundClient) {
            return res.status(404).json({ message: 'Client not found' });
        }

        foundClient.status = 'inactive';
        await foundClient.save();

        // Log client deactivation
        if (req.user) {
            await LogService.logAgentAction(req.user.id, `Deactivated client: ${foundClient.fullName}`);
        }

        res.json({
            message: 'Client deactivated successfully',
            client: foundClient
        });

    } catch (error) {
        console.error('Error deactivating client:', error);
        res.status(500).json({ message: 'Server error' });
    }
};
