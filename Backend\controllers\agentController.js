const Client = require('../models/client');

// Get clients
exports.getAllClients = async (req, res) => {
  try {
    const clients = await Client.find();
    res.json(clients);
  } catch (err) {
    res.status(500).json({ error: err.message });
  }
};

//Get client by name
exports.getClientByFullName = async (req, res) => {
  try {
    const fullName = req.params.fullName.trim();

    const foundClient = await Client.findOne({ fullName: new RegExp(`^${fullName}$`, 'i') });

    if (!foundClient) {
      return res.status(404).json({ message: 'Client not found' });
    }

    // Log client search
    if (req.user) {
      await LogService.logAgentAction(req.user.id, `Searched for client: ${fullName}`);
    }

    res.status(200).json(foundClient);
  } catch (error) {
    res.status(500).json({ error: 'Server error' });
  }
};

//Get client by card number

