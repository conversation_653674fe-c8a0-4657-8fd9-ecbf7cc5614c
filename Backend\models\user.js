const mongoose = require('mongoose');

const userSchema = new mongoose.Schema({
  firstName: {type: String, required: true},
  lastName: {type: String, required: true},
  email: { type: String, required: true },
  password: { type: String, required: true },
  branch: { type: String },
  role: { type: String, enum: ['agent', 'admin'], required: true },
  status: {
    type: String,
    enum: ['active', 'inactive'],
    required: false,
    default: undefined
  }
});

module.exports = mongoose.model('User', userSchema);