const express = require('express');
const router = express.Router();
const storageController = require('../controllers/storageController');


router.get('/all',storageController.getAllStorageSlots);
router.get('/available',storageController.getAvailableSlots);
router.get('/occupied',storageController.getOccupiedSlots);
router.get('/capacity-report',storageController.getStorageCapacityReport);
// Get storage utilization (Admin only)
router.get('/utilization',storageController.getStorageUtilization);
// Validate slot availability (Admin and Agent)
router.get('/validate/:storageLocation',storageController.validateSlotAvailability);
// Release storage slot (Admin and Agent)
router.put('/release/:storageLocation',storageController.releaseSlot);

module.exports = router;
