const express = require('express');
const router = express.Router();
const storageController = require('../controllers/storageController');


router.get('/all',storageController.getAllStorageSlots);
router.get('/available',storageController.getAvailableSlots);
router.get('/occupied',storageController.getOccupiedSlots);
router.get('/capacity-report',storageController.getStorageCapacityReport);
router.get('/utilization',storageController.getStorageUtilization);
router.get('/validate/:storageLocation',storageController.validateSlotAvailability);
router.put('/release/:storageLocation',storageController.releaseSlot);
router.get('/find-optimal',storageController.findOptimalSlot);

module.exports = router;
