// seedStorageSlots.js
const mongoose = require('mongoose');
const Storage = require('./models/storage');

const uri = 'mongodb://localhost:27017/atb';

async function seedSlots() {
    await mongoose.connect(uri);

    const rows = ['A', 'B', 'C'];      // Customize as needed
    const cols = 10;

    const slots = [];

    for (let row of rows) {
        for (let i = 1; i <= cols; i++) {
            slots.push({
                location: `${row}${i}`,
                occupied: false
            });
        }
    }

    await Storage.insertMany(slots);
    console.log(`${slots.length} slots inserted.`);
    await mongoose.disconnect();
}

seedSlots().catch(err => {
    console.error('Seeding error:', err);
});
