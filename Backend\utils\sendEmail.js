const nodemailer = require('nodemailer');
require('dotenv').config();

const sendEmail = async (to, firstName, email, rawPassword) => {
    const transporter = nodemailer.createTransport({
        service: 'gmail',
        auth: {
            user: process.env.EMAIL_USER,
            pass: process.env.EMAIL_PASS
        }
    });

    const mailOptions = {
        from: process.env.EMAIL_USER,
        to,
        subject: 'Your Agent Account Has Been Created',
        html: `
            <div style="font-family: Arial, sans-serif; line-height: 1.6; color: #333;">
                <h2>Hello ${firstName},</h2>
                <p>Your agent account has been successfully created by the administrator.</p>
                <p><strong>Email:</strong> ${email}</p>
                <p><strong>Password:</strong> ${rawPassword}</p>
                <p>You can now log in to the platform using these credentials.</p>
                <p>Please change your password after logging in for the first time.</p>
                <p>Best regards,</p>
            </div>
        `
    };

    try {
        const info = await transporter.sendMail(mailOptions);
        console.log("Email sent:", info.response);
    } catch (error) {
        console.error("Error sending email:", error);
    }
};

module.exports = sendEmail;
