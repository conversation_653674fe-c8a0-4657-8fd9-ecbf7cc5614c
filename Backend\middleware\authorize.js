// middlewares/authorize.js
const jwt = require('jsonwebtoken');

module.exports = function authorize(allowedRoles = []) {
    return (req, res, next) => {
        const token = req.session.token;
        if (!token) {
            return res.status(401).json({ message: 'Access denied' });
        }

        try {
            const decoded = jwt.verify(token, process.env.JWT_SECRET);
            const userRole = decoded.user.role;

            if (!allowedRoles.includes(userRole)) {
                return res.status(403).json({ message: 'Forbidden: Access denied' });
            }

            req.user = decoded.user; // Attach user data for later use
            next();
        } catch (err) {
            return res.status(401).json({ message: 'Invalid or expired token' });
        }
    };
};
