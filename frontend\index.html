<!doctype html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <link rel="icon" type="image/svg+xml" href="/src/assets/ATB-logo.png" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>ATB Credit Card Management System</title>
    <meta name="description" content="Arab Tunisian Bank - Credit Card Management and Tracking System" />
    <meta name="author" content="ATB - Arab Tunisian Bank" />

    <!-- Custom Styles for Header and Footer -->
    <style>
      * {
        margin: 0;
        padding: 0;
        box-sizing: border-box;
      }

      body {
        font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        min-height: 100vh;
        display: flex;
        flex-direction: column;
        background-color: #f5f7fa;
      }

      /* Header Styles */
      .app-header {
        background: linear-gradient(135deg, #1e3c72 0%, #2a5298 100%);
        color: white;
        padding: 1rem 2rem;
        box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        position: sticky;
        top: 0;
        z-index: 1000;
      }

      .header-content {
        max-width: 1200px;
        margin: 0 auto;
        display: flex;
        align-items: center;
        justify-content: space-between;
      }

      .logo-section {
        display: flex;
        align-items: center;
        gap: 1rem;
      }

      .logo-section img {
        height: 40px;
        width: auto;
      }

      .brand-info h1 {
        font-size: 1.5rem;
        font-weight: 600;
        margin-bottom: 0.2rem;
      }

      .brand-info p {
        font-size: 0.9rem;
        opacity: 0.9;
      }

      .header-actions {
        display: flex;
        align-items: center;
        gap: 1rem;
      }

      .user-info {
        display: flex;
        align-items: center;
        gap: 0.5rem;
        padding: 0.5rem 1rem;
        background: rgba(255,255,255,0.1);
        border-radius: 25px;
        font-size: 0.9rem;
      }

      /* Main Content Area */
      .main-container {
        flex: 1;
        max-width: 1200px;
        margin: 0 auto;
        width: 100%;
        padding: 2rem;
      }

      #root {
        width: 100%;
        min-height: 100%;
      }

      /* Footer Styles */
      .app-footer {
        background-color: #2c3e50;
        color: white;
        padding: 2rem 0 1rem;
        margin-top: auto;
      }

      .footer-content {
        max-width: 1200px;
        margin: 0 auto;
        padding: 0 2rem;
      }

      .footer-main {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
        gap: 2rem;
        margin-bottom: 2rem;
      }

      .footer-section h3 {
        color: #3498db;
        margin-bottom: 1rem;
        font-size: 1.1rem;
      }

      .footer-section p,
      .footer-section li {
        color: #bdc3c7;
        line-height: 1.6;
        margin-bottom: 0.5rem;
      }

      .footer-section ul {
        list-style: none;
      }

      .footer-section a {
        color: #bdc3c7;
        text-decoration: none;
        transition: color 0.3s ease;
      }

      .footer-section a:hover {
        color: #3498db;
      }

      .footer-bottom {
        border-top: 1px solid #34495e;
        padding-top: 1rem;
        text-align: center;
        color: #95a5a6;
        font-size: 0.9rem;
      }

      .footer-bottom p {
        margin-bottom: 0.5rem;
      }

      /* Responsive Design */
      @media (max-width: 768px) {
        .header-content {
          flex-direction: column;
          gap: 1rem;
          text-align: center;
        }

        .main-container {
          padding: 1rem;
        }

        .footer-main {
          grid-template-columns: 1fr;
          text-align: center;
        }
      }
    </style>
  </head>
  <body>
    <!-- Header -->
    <header class="app-header">
      <div class="header-content">
        <div class="logo-section">
          <img src="/src/assets/ATB-logo.png" alt="ATB Logo" />
          <div class="brand-info">
            <h1>ATB Card Management</h1>
            <p>Credit Card Tracking & Storage System</p>
          </div>
        </div>
        <div class="header-actions">
          <div class="user-info" id="user-info">
            <span>👤</span>
            <span id="user-name">Loading...</span>
          </div>
        </div>
      </div>
    </header>

    <!-- Main Content Container -->
    <main class="main-container">
      <div id="root"></div>
    </main>

    <!-- Footer -->
    <footer class="app-footer">
      <div class="footer-content">
        <div class="footer-main">
          <div class="footer-section">
            <h3>ATB - Arab Tunisian Bank</h3>
            <p>Leading financial institution in Tunisia, providing innovative banking solutions and services to individuals and businesses.</p>
            <p><strong>Established:</strong> 1982</p>
          </div>

          <div class="footer-section">
            <h3>System Information</h3>
            <ul>
              <li>Credit Card Management</li>
              <li>Storage & Tracking System</li>
              <li>OCR Card Scanning</li>
              <li>Predictive Analytics</li>
              <li>Real-time Monitoring</li>
            </ul>
          </div>

          <div class="footer-section">
            <h3>Support & Contact</h3>
            <ul>
              <li>📞 Phone: +216 71 XXX XXX</li>
              <li>📧 Email: <EMAIL></li>
              <li>🌐 Website: www.atb.com.tn</li>
              <li>📍 Head Office: Tunis, Tunisia</li>
            </ul>
          </div>

          <div class="footer-section">
            <h3>Quick Links</h3>
            <ul>
              <li><a href="#dashboard">Dashboard</a></li>
              <li><a href="#cards">Card Management</a></li>
              <li><a href="#storage">Storage System</a></li>
              <li><a href="#reports">Reports</a></li>
              <li><a href="#settings">Settings</a></li>
            </ul>
          </div>
        </div>

        <div class="footer-bottom">
          <p>&copy; 2024 Arab Tunisian Bank (ATB). All rights reserved.</p>
          <p>Credit Card Management System - Internal Use Only</p>
        </div>
      </div>
    </footer>

    <script type="module" src="/src/main.tsx"></script>

    <!-- User Info Script -->
    <script>
      // Update user info in header (you can integrate this with your auth system)
      document.addEventListener('DOMContentLoaded', function() {
        // This would typically get user info from your auth system
        const userInfo = localStorage.getItem('user') || sessionStorage.getItem('user');
        const userNameElement = document.getElementById('user-name');

        if (userInfo) {
          try {
            const user = JSON.parse(userInfo);
            userNameElement.textContent = user.firstName + ' ' + user.lastName || user.email || 'User';
          } catch (e) {
            userNameElement.textContent = 'Agent';
          }
        } else {
          userNameElement.textContent = 'Guest';
        }
      });
    </script>
  </body>
</html>
